# 临时邮箱系统快速部署指南

## 当前配置状态 ✅

已完成的配置：
- ✅ Cloudflare API Token: -qiZsH5l_6USQbVFTVd5oTrbbnCM1kettQV428Fc
- ✅ Zone ID: 5e0bebdd7954406af017d3ccc9de0d0a
- ✅ 域名: edu.nspojie.com
- ✅ 云函数配置文件已更新
- ✅ 前端 API 链接已准备（需要替换项目 ID）

## 立即需要完成的步骤

### 1. 获取 UniCloud 项目 ID（5分钟）

1. 登录 [UniCloud 控制台](https://unicloud.dcloud.net.cn/)
2. 选择您的项目
3. 在项目概览或设置页面找到项目 ID
4. 项目 ID 格式通常是：`mp-xxxxxxxx`

### 2. 更新前端配置（2分钟）

将以下文件中的 `[YOUR_PROJECT_ID]` 替换为实际的项目 ID：

**文件：`eduEmail-cloudflare-main/前端/script.js`**
- 第 52 行：generate-email 云函数 URL
- 第 209 行：Delete_edu_cloudfare 云函数 URL  
- 第 381 行：GET_cloudflare_edukg_email 云函数 URL

**文件：`eduEmail-cloudflare-main/cloudfare-workers后端/workers.js`**
- 第 249 行：POST_cloudflare_edukg_email 云函数 URL

### 3. 部署 Cloudflare Worker（5分钟）

1. 登录 [Cloudflare 控制台](https://dash.cloudflare.com/)
2. 选择您的域名 `edu.nspojie.com`
3. 进入 "Workers Routes" 或 "Workers & Pages"
4. 创建新的 Worker，名称：`orange-paper-039a`
5. 复制 `eduEmail-cloudflare-main/cloudfare-workers后端/workers.js` 的内容
6. 粘贴到 Worker 编辑器中
7. 点击 "Save and Deploy"

### 4. 配置邮件路由（3分钟）

1. 在 Cloudflare 控制台中
2. 选择域名 `edu.nspojie.com`
3. 进入 "Email" → "Email Routing"
4. 确保 Email Routing 已启用
5. MX 记录应该自动配置

### 5. 部署 UniCloud 云函数（10分钟）

1. 在 UniCloud 控制台中
2. 进入云函数管理
3. 逐个部署以下云函数：
   - `generate-email`
   - `POST_cloudflare_edukg_email`
   - `GET_cloudflare_edukg_email`
   - `Delete_edu_cloudfare`

4. 为每个云函数启用 HTTP 触发器
5. 记录每个云函数的 HTTP URL

### 6. 最终配置更新（3分钟）

使用获取到的实际云函数 URL 替换前端和 Workers 中的占位符。

## 测试流程

### 快速测试（5分钟）

1. 打开前端页面 `eduEmail-cloudflare-main/前端/index.html`
2. 点击"生成新的临时邮箱"
3. 如果成功，会显示类似：`<EMAIL>` 的邮箱地址
4. 使用任意邮箱发送测试邮件到生成的地址
5. 点击"查看邮件"按钮
6. 如果配置正确，应该能看到收到的邮件内容

### 故障排除

如果遇到问题：

1. **邮箱生成失败**：
   - 检查浏览器控制台错误
   - 确认云函数 URL 正确
   - 验证 Cloudflare API Token 权限

2. **邮件接收失败**：
   - 确认 Worker 已正确部署
   - 检查 Email Routing 状态
   - 验证 MX 记录配置

3. **前端报错**：
   - 检查 CORS 配置
   - 确认所有云函数 URL 已正确替换
   - 验证网络连接

## 预期结果

完成配置后，您将拥有：
- ✅ 可以生成临时邮箱地址
- ✅ 可以接收发送到临时邮箱的邮件
- ✅ 可以在网页上查看邮件内容
- ✅ 可以删除临时邮箱和相关数据

## 需要帮助？

如果在任何步骤遇到问题，请提供：
1. 具体的错误信息
2. 浏览器控制台截图
3. 当前进行到哪一步

我会帮您快速解决问题！
