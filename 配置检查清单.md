# 临时邮箱系统配置检查清单

## 1. Cloudflare 域名配置检查

### 域名信息
- **域名**: edu.nspojie.com
- **Zone ID**: 5e0bebdd7954406af017d3ccc9de0d0a
- **API Token**: -qiZsH5l_6USQbVFTVd5oTrbbnCM1kettQV428Fc

### 需要验证的配置项

#### 1.1 DNS 记录
- [ ] 确认域名 edu.nspojie.com 已添加到 Cloudflare
- [ ] 确认 DNS 记录正确指向您的服务器
- [ ] 确认域名状态为 "Active"

#### 1.2 Email Routing 配置
- [ ] 在 Cloudflare 控制台中启用 Email Routing
- [ ] 确认 MX 记录已自动配置
- [ ] 验证邮件路由状态为 "Active"

#### 1.3 Workers 配置
- [ ] 创建 Cloudflare Worker (名称: orange-paper-039a)
- [ ] 将 workers.js 代码部署到 Worker
- [ ] 配置 Worker 路由到域名

## 2. UniCloud 配置检查

### 2.1 云函数部署
- [ ] 部署 generate-email 云函数
- [ ] 部署 POST_cloudflare_edukg_email 云函数
- [ ] 部署 GET_cloudflare_edukg_email 云函数
- [ ] 部署 Delete_edu_cloudfare 云函数

### 2.2 数据库配置
- [ ] 创建 temp_emails 集合
- [ ] 创建 email_data 集合
- [ ] 配置数据库权限

### 2.3 获取云函数 URL
您需要在 UniCloud 控制台中获取每个云函数的 HTTP 触发器 URL，格式通常为：
```
https://fc-mp-[PROJECT_ID].next.bspapp.com/[FUNCTION_NAME]
```

## 3. 前端配置

### 3.1 更新 API 链接
需要将以下占位符替换为实际的云函数 URL：
- `https://fc-mp-[YOUR_PROJECT_ID].next.bspapp.com/generate-email`
- `https://fc-mp-[YOUR_PROJECT_ID].next.bspapp.com/Delete_edu_cloudfare`
- `https://fc-mp-[YOUR_PROJECT_ID].next.bspapp.com/GET_cloudflare_edukg_email`

## 4. 测试步骤

### 4.1 基础功能测试
1. [ ] 打开前端页面
2. [ ] 点击"生成新的临时邮箱"
3. [ ] 确认邮箱地址正确生成
4. [ ] 发送测试邮件到生成的地址
5. [ ] 点击"查看邮件"确认能收到邮件
6. [ ] 测试"清除邮箱"功能

### 4.2 错误排查
如果遇到问题，检查：
- [ ] 浏览器控制台是否有错误
- [ ] Cloudflare Workers 日志
- [ ] UniCloud 云函数日志
- [ ] 网络请求是否成功

## 5. 下一步操作

1. **获取 UniCloud 项目 ID**：
   - 登录 UniCloud 控制台
   - 在项目设置中找到项目 ID
   - 替换前端和 Workers 中的占位符

2. **部署 Cloudflare Worker**：
   - 在 Cloudflare 控制台创建新的 Worker
   - 复制 workers.js 代码
   - 配置路由规则

3. **测试完整流程**：
   - 生成邮箱 → 发送邮件 → 接收邮件 → 查看邮件

## 6. 常见问题

### 6.1 邮箱生成失败
- 检查 API Token 权限
- 确认 Zone ID 正确
- 检查域名配置

### 6.2 邮件接收失败
- 确认 MX 记录配置
- 检查 Worker 部署状态
- 验证邮件路由规则

### 6.3 前端无法访问云函数
- 检查 CORS 配置
- 确认云函数 URL 正确
- 验证网络连接
