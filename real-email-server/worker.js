export default {
    async fetch(request, env, ctx) {
        return new Response('邮件处理Worker运行中', { status: 200 });
    },

    async email(message, env, ctx) {
        try {
            console.log('🚀 开始处理邮件');
            console.log('📧 发件人:', message.from, '| 收件人:', message.to);

            // 获取原始邮件内容
            const response = new Response(message.raw);
            const arrayBuffer = await response.arrayBuffer();
            const rawText = new TextDecoder().decode(arrayBuffer);

            console.log('📥 原始邮件大小:', rawText.length, '字符');

            // 分离头部和正文
            const [headers, ...bodyParts] = rawText.split('\r\n\r\n');
            const body = bodyParts.join('\r\n\r\n');

            // 解析头部
            const parsedHeaders = this.parseHeaders(headers);

            // 解码主题
            const subject = this.decodeSubject(parsedHeaders.subject || '');

            // 检查是否为多部分邮件
            const contentType = parsedHeaders['content-type'] || '';
            const isMultipart = contentType.includes('multipart');

            console.log('🔍 邮件类型:', isMultipart ? '多部分' : '单部分');

            let emailContent = '';
            let htmlContent = '';

            if (isMultipart) {
                const result = this.parseMultipartEmail(body, contentType);
                emailContent = result.text;
                htmlContent = result.html;
            } else {
                emailContent = this.decodeContent(body, parsedHeaders);
            }

            // 如果没有纯文本内容，尝试从HTML中提取
            if (!emailContent && htmlContent) {
                emailContent = this.extractTextFromHtml(htmlContent);
            }

            console.log('✅ 邮件解析完成');
            console.log('📝 主题:', subject);
            console.log('📄 文本长度:', emailContent.length);
            console.log('🌐 HTML长度:', htmlContent.length);

            // 发送到您的服务器
            await this.sendToServer(message, subject, emailContent, htmlContent);

            console.log('🎯 邮件处理完成');
            return new Response('邮件处理成功', { status: 200 });

        } catch (error) {
            console.error('❌ 邮件处理错误:', error);
            return new Response('邮件处理失败', { status: 500 });
        }
    },

    // 解析邮件头部
    parseHeaders(headers) {
        const parsedHeaders = {};
        const headerLines = headers.split('\r\n');
        let currentHeader = '';

        for (const line of headerLines) {
            if (line.match(/^\s/)) {
                if (currentHeader) {
                    parsedHeaders[currentHeader] += ' ' + line.trim();
                }
            } else {
                const colonIndex = line.indexOf(':');
                if (colonIndex > 0) {
                    currentHeader = line.substring(0, colonIndex).toLowerCase();
                    parsedHeaders[currentHeader] = line.substring(colonIndex + 1).trim();
                }
            }
        }

        return parsedHeaders;
    },

    // 解码邮件主题
    decodeSubject(subject) {
        if (!subject) return '';

        return subject.replace(/=\?([^?]+)\?([BQ])\?([^?]+)\?=/gi, (match, charset, encoding, encodedText) => {
            try {
                if (encoding.toUpperCase() === 'Q') {
                    return decodeURIComponent(encodedText.replace(/=/g, '%').replace(/_/g, ' '));
                } else if (encoding.toUpperCase() === 'B') {
                    return atob(encodedText);
                }
            } catch (e) {
                console.warn('主题解码失败:', e);
                return encodedText;
            }
            return match;
        });
    },

    // 解析多部分邮件
    parseMultipartEmail(body, contentType) {
        const result = { text: '', html: '' };

        try {
            const boundaryMatch = contentType.match(/boundary[=:][\s]*["']?([^"'\s;]+)["']?/i);
            if (!boundaryMatch) {
                console.warn('未找到boundary');
                return result;
            }

            const boundary = boundaryMatch[1];
            const parts = body.split(`--${boundary}`);

            for (let i = 0; i < parts.length; i++) {
                const part = parts[i].trim();
                if (!part || part === '--') continue;

                const [partHeaders, ...contentParts] = part.split('\r\n\r\n');
                if (contentParts.length === 0) continue;

                const partContent = contentParts.join('\r\n\r\n');
                const partHeadersLower = partHeaders.toLowerCase();
                const partHeadersObj = this.parseHeaders(partHeaders);

                if (partHeadersLower.includes('content-type: text/plain')) {
                    result.text = this.decodeContent(partContent, partHeadersObj);
                } else if (partHeadersLower.includes('content-type: text/html')) {
                    result.html = this.decodeContent(partContent, partHeadersObj);
                } else if (partHeadersLower.includes('multipart')) {
                    const nestedResult = this.parseMultipartEmail(partContent, partHeaders);
                    if (nestedResult.text) result.text = nestedResult.text;
                    if (nestedResult.html) result.html = nestedResult.html;
                }
            }
        } catch (error) {
            console.error('多部分解析错误:', error);
        }

        return result;
    },

    // 解码内容
    decodeContent(content, headers) {
        let decoded = content;
        const encoding = headers['content-transfer-encoding'] || '';

        if (encoding.toLowerCase().includes('quoted-printable')) {
            decoded = decoded
                .replace(/=\r\n/g, '')
                .replace(/=([0-9A-F]{2})/gi, (match, hex) => {
                    return String.fromCharCode(parseInt(hex, 16));
                });
        } else if (encoding.toLowerCase().includes('base64')) {
            try {
                decoded = atob(decoded.replace(/\s/g, ''));
            } catch (e) {
                console.warn('Base64解码失败:', e);
            }
        }

        return decoded.trim();
    },

    // 从HTML中提取纯文本
    extractTextFromHtml(html) {
        return html
            .replace(/<style[^>]*>.*?<\/style>/gis, '')
            .replace(/<script[^>]*>.*?<\/script>/gis, '')
            .replace(/<[^>]+>/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();
    },

    // 发送到您的服务器
    async sendToServer(message, subject, textContent, htmlContent) {
        console.log('📡 发送邮件数据到服务器...');

        // 您的服务器地址 - 需要替换为实际地址
        const serverUrl = 'https://edu.nspojie.com/api/receive-email';

        const payload = {
            emailInfo: {
                from: message.from,
                to: message.to,
                subject: subject,
                date: new Date().toISOString()
            },
            emailContent: {
                text: textContent,
                html: htmlContent
            }
        };

        try {
            const response = await fetch(serverUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Cloudflare-Workers-Email-Processor/1.0'
                },
                body: JSON.stringify(payload)
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ 服务器响应成功:', result);
            } else {
                const errorText = await response.text();
                console.error('❌ 服务器响应错误:', response.status, errorText);
            }
        } catch (error) {
            console.error('❌ 发送到服务器失败:', error);
        }
    }
};
